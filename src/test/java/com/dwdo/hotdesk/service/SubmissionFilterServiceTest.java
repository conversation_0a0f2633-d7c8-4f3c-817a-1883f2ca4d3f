package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for SubmissionFilterService
 * Tests date parsing, filter specification building, and submission filtering functionality
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Submission Filter Service Tests")
@SuppressWarnings("unchecked")
class SubmissionFilterServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @InjectMocks
    private SubmissionFilterService submissionFilterService;

    private Submission mockSubmission;
    private Pageable mockPageable;

    @BeforeEach
    void setUp() {
        mockSubmission = createMockSubmission();
        mockPageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
    }

    @Nested
    @DisplayName("Date Parsing Tests")
    class DateParsingTests {

        @Test
        @DisplayName("Should parse valid start date string to LocalDateTime")
        void testParseStartDate_ValidDateString() {
            // Given
            String dateStr = "2024-01-15";

            // When
            LocalDateTime result = submissionFilterService.parseStartDate(dateStr);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.toLocalDate()).isEqualTo(LocalDate.of(2024, 1, 15));
            assertThat(result.toLocalTime()).isEqualTo(LocalDateTime.MIN.toLocalTime()); // Start of day
        }

        @Test
        @DisplayName("Should parse valid end date string to LocalDateTime")
        void testParseEndDate_ValidDateString() {
            // Given
            String dateStr = "2024-01-15";

            // When
            LocalDateTime result = submissionFilterService.parseEndDate(dateStr);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.toLocalDate()).isEqualTo(LocalDate.of(2024, 1, 15));
            assertThat(result.getHour()).isEqualTo(23);
            assertThat(result.getMinute()).isEqualTo(59);
            assertThat(result.getSecond()).isEqualTo(59);
        }

        @Test
        @DisplayName("Should parse valid ISO datetime string")
        void testParseStartDate_ValidISODateTime() {
            // Given
            String dateTimeStr = "2024-01-15T10:30:00";

            // When
            LocalDateTime result = submissionFilterService.parseStartDate(dateTimeStr);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).isEqualTo(LocalDateTime.of(2024, 1, 15, 10, 30, 0));
        }

        @Test
        @DisplayName("Should return null for null start date")
        void testParseStartDate_NullInput() {
            // When
            LocalDateTime result = submissionFilterService.parseStartDate(null);

            // Then
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Should return null for empty start date")
        void testParseStartDate_EmptyInput() {
            // When
            LocalDateTime result = submissionFilterService.parseStartDate("");

            // Then
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Should throw exception for invalid start date format")
        void testParseStartDate_InvalidFormat() {
            // Given
            String invalidDateStr = "invalid-date";

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.parseStartDate(invalidDateStr))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Invalid start date format: invalid-date");
        }

        @Test
        @DisplayName("Should return null for null end date")
        void testParseEndDate_NullInput() {
            // When
            LocalDateTime result = submissionFilterService.parseEndDate(null);

            // Then
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Should return null for empty end date")
        void testParseEndDate_EmptyInput() {
            // When
            LocalDateTime result = submissionFilterService.parseEndDate("");

            // Then
            assertThat(result).isNull();
        }

        @Test
        @DisplayName("Should throw exception for invalid end date format")
        void testParseEndDate_InvalidFormat() {
            // Given
            String invalidDateStr = "2024-13-45"; // Invalid month and day

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.parseEndDate(invalidDateStr))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Invalid end date format: 2024-13-45");
        }

        @Test
        @DisplayName("Should handle different date formats gracefully")
        void testParseDates_DifferentFormats() {
            // Test various valid date formats
            assertThat(submissionFilterService.parseStartDate("2024-01-01")).isNotNull();
            assertThat(submissionFilterService.parseEndDate("2024-12-31")).isNotNull();
            
            // Test ISO datetime format
            assertThat(submissionFilterService.parseStartDate("2024-01-01T00:00:00")).isNotNull();
            assertThat(submissionFilterService.parseEndDate("2024-12-31T23:59:59")).isNotNull();
        }
    }

    @Nested
    @DisplayName("Date Range Validation Tests")
    class DateRangeValidationTests {

        @Test
        @DisplayName("Should pass validation when start date is before end date")
        void testValidateDateRange_ValidRange() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            // When & Then - Should not throw exception
            submissionFilterService.validateDateRange(startDate, endDate);
        }

        @Test
        @DisplayName("Should pass validation when both dates are null")
        void testValidateDateRange_BothNull() {
            // When & Then - Should not throw exception
            submissionFilterService.validateDateRange(null, null);
        }

        @Test
        @DisplayName("Should pass validation when start date is null")
        void testValidateDateRange_StartDateNull() {
            // Given
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            // When & Then - Should not throw exception
            submissionFilterService.validateDateRange(null, endDate);
        }

        @Test
        @DisplayName("Should pass validation when end date is null")
        void testValidateDateRange_EndDateNull() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);

            // When & Then - Should not throw exception
            submissionFilterService.validateDateRange(startDate, null);
        }

        @Test
        @DisplayName("Should pass validation when dates are equal")
        void testValidateDateRange_EqualDates() {
            // Given
            LocalDateTime sameDate = LocalDateTime.of(2024, 6, 15, 12, 0);

            // When & Then - Should not throw exception
            submissionFilterService.validateDateRange(sameDate, sameDate);
        }

        @Test
        @DisplayName("Should throw exception when start date is after end date")
        void testValidateDateRange_StartAfterEnd() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 12, 31, 23, 59);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 1, 0, 0);

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.validateDateRange(startDate, endDate))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Start date must be before end date")
                    .hasMessageContaining("2024-12-31")
                    .hasMessageContaining("2024-01-01");
        }

        @Test
        @DisplayName("Should throw exception when start date is one day after end date")
        void testValidateDateRange_OneDayDifference() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 6, 16, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 6, 15, 23, 59);

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.validateDateRange(startDate, endDate))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Start date must be before end date")
                    .hasMessageContaining("2024-06-16")
                    .hasMessageContaining("2024-06-15");
        }

        @Test
        @DisplayName("Should pass validation and log partial filtering when only start date provided")
        void testValidateDateRange_OnlyStartDate() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);

            // When & Then - Should not throw exception and should log partial filtering
            submissionFilterService.validateDateRange(startDate, null);
        }

        @Test
        @DisplayName("Should pass validation and log partial filtering when only end date provided")
        void testValidateDateRange_OnlyEndDate() {
            // Given
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            // When & Then - Should not throw exception and should log partial filtering
            submissionFilterService.validateDateRange(null, endDate);
        }

        @Test
        @DisplayName("Should pass validation and log no filtering when both dates are null")
        void testValidateDateRange_BothNullWithLogging() {
            // When & Then - Should not throw exception and should log no filtering
            submissionFilterService.validateDateRange(null, null);
        }
    }

    @Nested
    @DisplayName("Fetch Submissions Tests")
    class FetchSubmissionsTests {

        @Test
        @DisplayName("Should successfully fetch submissions with all filters")
        void testFetchSubmissions_Success_AllFilters() {
            // Given
            String userNIP = "user123";
            String status = "PENDING";
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);
            String nip = "123456789";
            String name = "John";

            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    userNIP, status, startDate, endDate, nip, name, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getTotalElements()).isEqualTo(1);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should successfully fetch submissions with minimal filters")
        void testFetchSubmissions_Success_MinimalFilters() {
            // Given
            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    null, null, null, null, null, null, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should handle empty results")
        void testFetchSubmissions_EmptyResults() {
            // Given
            Page<Submission> emptyPage = new PageImpl<>(Arrays.asList(), mockPageable, 0);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(emptyPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    "user123", "PENDING", null, null, null, null, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).isEmpty();
            assertThat(result.getTotalElements()).isEqualTo(0);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should handle repository exception")
        void testFetchSubmissions_RepositoryException() {
            // Given
            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.fetchSubmissions(
                    "user123", "PENDING", null, null, null, null, null, mockPageable))
                    .isInstanceOf(RuntimeException.class)
                    .hasMessageContaining("Database connection failed");

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should throw exception when start date is after end date in fetchSubmissions")
        void testFetchSubmissions_InvalidDateRange() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 12, 31, 23, 59);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 1, 0, 0);

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.fetchSubmissions(
                    "user123", "PENDING", startDate, endDate, null, null, null, mockPageable))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Start date must be before end date");

            // Verify repository is not called when validation fails
            verify(submissionRepository, times(0)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should pass validation and fetch submissions when date range is valid")
        void testFetchSubmissions_ValidDateRange() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    "user123", "PENDING", startDate, endDate, null, null, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should fetch submissions with only start date (from start date onwards)")
        void testFetchSubmissions_OnlyStartDate() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 6, 1, 0, 0);

            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    "user123", "PENDING", startDate, null, null, null, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should fetch submissions with only end date (up to end date)")
        void testFetchSubmissions_OnlyEndDate() {
            // Given
            LocalDateTime endDate = LocalDateTime.of(2024, 6, 30, 23, 59);

            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    "user123", "PENDING", null, endDate, null, null, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }
    }

    @Nested
    @DisplayName("Filter Specification Tests")
    class FilterSpecificationTests {

        @Test
        @DisplayName("Should build specification with all filters")
        void testBuildFilterSpecification_AllFilters() {
            // Given
            String userNIP = "user123";
            String status = "PENDING";
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);
            String nip = "123456789";
            String name = "John";

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    userNIP, status, startDate, endDate, nip, name, null);

            // Then
            assertThat(result).isNotNull();
            // Note: Testing the actual specification logic would require integration tests
            // Here we verify that the method returns a non-null specification
        }

        @Test
        @DisplayName("Should build specification with null filters")
        void testBuildFilterSpecification_NullFilters() {
            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    null, null, null, null, null, null, null);

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should build specification with partial filters")
        void testBuildFilterSpecification_PartialFilters() {
            // Given
            String status = "APPROVED";
            String nip = "987654321";

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    null, status, null, null, nip, null, null);

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should throw exception when start date is after end date in buildFilterSpecification")
        void testBuildFilterSpecification_InvalidDateRange() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 12, 31, 23, 59);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 1, 0, 0);

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.buildFilterSpecification(
                    "user123", "PENDING", startDate, endDate, null, null, null))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Start date must be before end date")
                    .hasMessageContaining("2024-12-31")
                    .hasMessageContaining("2024-01-01");
        }

        @Test
        @DisplayName("Should build specification successfully when date range is valid")
        void testBuildFilterSpecification_ValidDateRange() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    "user123", "PENDING", startDate, endDate, null, null, null);

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should build specification with only start date (from start date onwards)")
        void testBuildFilterSpecification_OnlyStartDate() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 6, 1, 0, 0);

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    "user123", "PENDING", startDate, null, null, null, null);

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should build specification with only end date (up to end date)")
        void testBuildFilterSpecification_OnlyEndDate() {
            // Given
            LocalDateTime endDate = LocalDateTime.of(2024, 6, 30, 23, 59);

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    "user123", "PENDING", null, endDate, null, null, null);

            // Then
            assertThat(result).isNotNull();
        }
    }

    @Nested
    @DisplayName("Pagination and Sorting Tests")
    class PaginationAndSortingTests {

        @Test
        @DisplayName("Should create pageable with default sorting (DESC)")
        void testCreatePageable_DefaultSorting() {
            // Given
            int page = 0;
            int size = 10;
            String[] sort = null;

            // When
            Pageable result = submissionFilterService.createPageable(page, size, sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getPageNumber()).isEqualTo(0);
            assertThat(result.getPageSize()).isEqualTo(10);
            assertThat(result.getSort().getOrderFor("id")).isNotNull();
            assertThat(result.getSort().getOrderFor("id").getDirection()).isEqualTo(Sort.Direction.DESC);
        }

        @Test
        @DisplayName("Should create pageable with ASC sorting")
        void testCreatePageable_AscSorting() {
            // Given
            int page = 1;
            int size = 20;
            String[] sort = {"asc"};

            // When
            Pageable result = submissionFilterService.createPageable(page, size, sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getPageNumber()).isEqualTo(1);
            assertThat(result.getPageSize()).isEqualTo(20);
            assertThat(result.getSort().getOrderFor("id")).isNotNull();
            assertThat(result.getSort().getOrderFor("id").getDirection()).isEqualTo(Sort.Direction.ASC);
        }

        @Test
        @DisplayName("Should create pageable with DESC sorting when not specified as ASC")
        void testCreatePageable_DescSortingDefault() {
            // Given
            int page = 2;
            int size = 5;
            String[] sort = {"desc", "other"};

            // When
            Pageable result = submissionFilterService.createPageable(page, size, sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getPageNumber()).isEqualTo(2);
            assertThat(result.getPageSize()).isEqualTo(5);
            assertThat(result.getSort().getOrderFor("id")).isNotNull();
            assertThat(result.getSort().getOrderFor("id").getDirection()).isEqualTo(Sort.Direction.DESC);
        }

        @Test
        @DisplayName("Should create sort orders with default DESC direction")
        void testCreateSortOrders_DefaultDirection() {
            // Given
            String[] sort = null;

            // When
            List<Sort.Order> result = submissionFilterService.createSortOrders(sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getProperty()).isEqualTo("id");
            assertThat(result.get(0).getDirection()).isEqualTo(Sort.Direction.DESC);
        }

        @Test
        @DisplayName("Should create sort orders with ASC direction")
        void testCreateSortOrders_AscDirection() {
            // Given
            String[] sort = {"asc"};

            // When
            List<Sort.Order> result = submissionFilterService.createSortOrders(sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getProperty()).isEqualTo("id");
            assertThat(result.get(0).getDirection()).isEqualTo(Sort.Direction.ASC);
        }

        @Test
        @DisplayName("Should create sort orders with ASC direction when ASC is anywhere in array")
        void testCreateSortOrders_AscInMiddle() {
            // Given
            String[] sort = {"field1", "asc", "field2"};

            // When
            List<Sort.Order> result = submissionFilterService.createSortOrders(sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getProperty()).isEqualTo("id");
            assertThat(result.get(0).getDirection()).isEqualTo(Sort.Direction.ASC);
        }

        @Test
        @DisplayName("Should create sort orders with DESC direction when ASC not present")
        void testCreateSortOrders_NoAscPresent() {
            // Given
            String[] sort = {"field1", "desc", "field2"};

            // When
            List<Sort.Order> result = submissionFilterService.createSortOrders(sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getProperty()).isEqualTo("id");
            assertThat(result.get(0).getDirection()).isEqualTo(Sort.Direction.DESC);
        }

        @Test
        @DisplayName("Should handle empty sort array")
        void testCreateSortOrders_EmptyArray() {
            // Given
            String[] sort = {};

            // When
            List<Sort.Order> result = submissionFilterService.createSortOrders(sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getProperty()).isEqualTo("id");
            assertThat(result.get(0).getDirection()).isEqualTo(Sort.Direction.DESC);
        }

        @Test
        @DisplayName("Should handle case insensitive ASC detection")
        void testCreateSortOrders_CaseInsensitiveAsc() {
            // Given
            String[] sort = {"ASC", "DESC"};

            // When
            List<Sort.Order> result = submissionFilterService.createSortOrders(sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0).getProperty()).isEqualTo("id");
            assertThat(result.get(0).getDirection()).isEqualTo(Sort.Direction.ASC);
        }
    }

    @Nested
    @DisplayName("Edge Cases and Integration Tests")
    class EdgeCasesAndIntegrationTests {

        @Test
        @DisplayName("Should handle very large page size")
        void testCreatePageable_LargePageSize() {
            // Given
            int page = 0;
            int size = 1000;
            String[] sort = {"asc"};

            // When
            Pageable result = submissionFilterService.createPageable(page, size, sort);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getPageSize()).isEqualTo(1000);
            assertThat(result.getSort().getOrderFor("id").getDirection()).isEqualTo(Sort.Direction.ASC);
        }

        @Test
        @DisplayName("Should handle date parsing with leap year")
        void testParseDates_LeapYear() {
            // Given
            String leapYearDate = "2024-02-29"; // 2024 is a leap year

            // When
            LocalDateTime startResult = submissionFilterService.parseStartDate(leapYearDate);
            LocalDateTime endResult = submissionFilterService.parseEndDate(leapYearDate);

            // Then
            assertThat(startResult).isNotNull();
            assertThat(startResult.toLocalDate()).isEqualTo(LocalDate.of(2024, 2, 29));
            assertThat(endResult).isNotNull();
            assertThat(endResult.toLocalDate()).isEqualTo(LocalDate.of(2024, 2, 29));
        }

        @Test
        @DisplayName("Should handle date parsing with invalid leap year")
        void testParseDates_InvalidLeapYear() {
            // Given
            String invalidLeapYearDate = "2023-02-29"; // 2023 is not a leap year

            // When & Then
            assertThatThrownBy(() -> submissionFilterService.parseStartDate(invalidLeapYearDate))
                    .isInstanceOf(CustomBadRequestException.class);

            assertThatThrownBy(() -> submissionFilterService.parseEndDate(invalidLeapYearDate))
                    .isInstanceOf(CustomBadRequestException.class);
        }

        @Test
        @DisplayName("Should handle whitespace in date strings")
        void testParseDates_WithWhitespace() {
            // Given
            String dateWithWhitespace = "  2024-01-15  ";

            // When & Then
            // The current implementation doesn't trim whitespace, so this should fail
            assertThatThrownBy(() -> submissionFilterService.parseStartDate(dateWithWhitespace))
                    .isInstanceOf(CustomBadRequestException.class);
        }

        @Test
        @DisplayName("Should handle fetch submissions with multiple submissions")
        void testFetchSubmissions_MultipleResults() {
            // Given
            Submission submission1 = createMockSubmission();
            Submission submission2 = createMockSubmission();
            submission2.setId(2L);
            submission2.setReferenceNumber("REF-002");
            submission2.setNip("987654321");
            submission2.setName("Jane Smith");

            List<Submission> submissions = Arrays.asList(submission1, submission2);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 2);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    null, "PENDING", null, null, null, null, null, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(2);
            assertThat(result.getTotalElements()).isEqualTo(2);
            assertThat(result.getContent().get(0).getId()).isEqualTo(1L);
            assertThat(result.getContent().get(1).getId()).isEqualTo(2L);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }

        @Test
        @DisplayName("Should handle date range where start date is after end date")
        void testBuildFilterSpecification_StartDateAfterEndDate() {
            // Given
            LocalDateTime startDate = LocalDateTime.of(2024, 12, 31, 23, 59);
            LocalDateTime endDate = LocalDateTime.of(2024, 1, 1, 0, 0);

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    null, null, startDate, endDate, null, null, null);

            // Then
            // The service should still create a specification, even if the date range is invalid
            // The actual filtering logic would handle this at the database level
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should handle empty string filters")
        void testBuildFilterSpecification_EmptyStrings() {
            // Given
            String emptyUserNIP = "";
            String emptyStatus = "";
            String emptyNip = "";
            String emptyName = "";

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    emptyUserNIP, emptyStatus, null, null, emptyNip, emptyName, null);

            // Then
            assertThat(result).isNotNull();
            // Empty strings should be treated as null/no filter
        }

        @Test
        @DisplayName("Should handle special characters in search filters")
        void testBuildFilterSpecification_SpecialCharacters() {
            // Given
            String nipWithSpecialChars = "123-456-789";
            String nameWithSpecialChars = "O'Connor";

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    null, null, null, null, nipWithSpecialChars, nameWithSpecialChars, null);

            // Then
            assertThat(result).isNotNull();
            // The specification should handle special characters properly
        }

        @Test
        @DisplayName("Should handle very long filter strings")
        void testBuildFilterSpecification_LongStrings() {
            // Given
            String longNip = "1".repeat(100);
            String longName = "A".repeat(200);

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    null, null, null, null, longNip, longName, null);

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should build specification with payment type filter")
        void testBuildFilterSpecification_WithPaymentType() {
            // Given
            String paymentType = "Bonus Staggered";

            // When
            Specification<Submission> result = submissionFilterService.buildFilterSpecification(
                    null, null, null, null, null, null, paymentType);

            // Then
            assertThat(result).isNotNull();
        }

        @Test
        @DisplayName("Should successfully fetch submissions with payment type filter")
        void testFetchSubmissions_Success_WithPaymentType() {
            // Given
            String paymentType = "Salary Adjustment";
            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(submissionRepository.findAll(any(Specification.class), eq(mockPageable)))
                    .thenReturn(submissionPage);

            // When
            Page<Submission> result = submissionFilterService.fetchSubmissions(
                    null, null, null, null, null, null, paymentType, mockPageable);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getTotalElements()).isEqualTo(1);

            verify(submissionRepository, times(1)).findAll(any(Specification.class), eq(mockPageable));
        }
    }

    /**
     * Helper method to create a mock Submission entity
     */
    private Submission createMockSubmission() {
        Submission submission = new Submission();
        submission.setId(1L);
        submission.setReferenceNumber("REF-001");
        submission.setSubmitterName("John Doe");
        submission.setSubmitterJob("Developer");
        submission.setStatus("PENDING");
        submission.setNip("123456789");
        submission.setName("John Doe");
        submission.setGrade("A");
        submission.setPaymentType("Salary");
        submission.setAmount(new BigDecimal("5000000"));
        submission.setDescription("Monthly salary");
        submission.setMonthOfProcess("January");
        submission.setYearOfProcess("2024");
        submission.setDirectorate("IT");
        submission.setSlik("-");
        submission.setSanction("-");
        submission.setTerminationDate("-");
        submission.setEligible(true);
        submission.setCurrentReviewer("");
        return submission;
    }
}
