# Database Migration Instructions for <PERSON><PERSON><PERSON>

## Overview
This migration moves the `payment_date` field from the `submission` table to the `submission_payment` table and ensures proper one-to-one relationship between the tables.

## Prerequisites
1. **BACKUP YOUR DATABASE** before running any migration scripts
2. Test on development/staging environment first
3. Ensure no active transactions are running during migration
4. Have <PERSON><PERSON><PERSON> connected to your MySQL database

## Migration Steps

### Step 1: Verify Current Schema
Run this query to check current table structures:

```sql
-- Check submission table structure
DESCRIBE submission;

-- Check submission_payment table structure (if exists)
DESCRIBE submission_payment;

-- Count current records
SELECT 'submission' as table_name, COUNT(*) as count FROM submission
UNION ALL
SELECT 'submission_payment' as table_name, COUNT(*) as count FROM submission_payment;
```

### Step 2: Create submission_payment table (if not exists)
```sql
CREATE TABLE IF NOT EXISTS `submission_payment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `reference_number` VARCHAR(255) NOT NULL,
    `task_name` VA<PERSON>HA<PERSON>(255) NULL,
    `current_reviewer` LONGTEXT NULL,
    `payment_date` DATE NULL,
    `created_at` DATETIME NOT NULL,
    `updated_at` DATETIME NULL,
    `created_by` VARCHAR(255) NULL,
    `updated_by` VARCHAR(255) NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_submission_payment_reference_number` (`reference_number`),
    INDEX `idx_submission_payment_task_name` (`task_name`),
    INDEX `idx_submission_payment_reference_number` (`reference_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

### Step 3: Migrate Data
```sql
-- Insert records for all submissions that don't have submission_payment record
INSERT INTO `submission_payment` (
    `reference_number`, 
    `task_name`, 
    `current_reviewer`, 
    `payment_date`, 
    `created_at`, 
    `updated_at`, 
    `created_by`, 
    `updated_by`
)
SELECT 
    s.`reference_number`,
    NULL as `task_name`,  -- Different from submission.task_name as per requirement
    NULL as `current_reviewer`,  -- Different from submission.current_reviewer as per requirement
    s.`payment_date`,  -- Migrate payment_date from submission
    NOW() as `created_at`,
    NOW() as `updated_at`,
    'MIGRATION_SCRIPT' as `created_by`,
    'MIGRATION_SCRIPT' as `updated_by`
FROM `submission` s
WHERE s.`reference_number` NOT IN (
    SELECT sp.`reference_number` 
    FROM `submission_payment` sp
)
ON DUPLICATE KEY UPDATE
    `payment_date` = COALESCE(VALUES(`payment_date`), `submission_payment`.`payment_date`),
    `updated_at` = NOW(),
    `updated_by` = 'MIGRATION_SCRIPT';
```

### Step 4: Verify Migration
```sql
-- Verify record counts match
SELECT 
    'Submission records' as description,
    COUNT(*) as count
FROM `submission`
UNION ALL
SELECT 
    'SubmissionPayment records' as description,
    COUNT(*) as count
FROM `submission_payment`
UNION ALL
SELECT 
    'Submissions with payment_date' as description,
    COUNT(*) as count
FROM `submission`
WHERE `payment_date` IS NOT NULL
UNION ALL
SELECT 
    'SubmissionPayment with payment_date' as description,
    COUNT(*) as count
FROM `submission_payment`
WHERE `payment_date` IS NOT NULL;

-- Check for any missing relationships
SELECT s.reference_number 
FROM submission s 
LEFT JOIN submission_payment sp ON s.reference_number = sp.reference_number 
WHERE sp.reference_number IS NULL;
```

### Step 5: Test Application
1. Deploy updated application code with SubmissionPayment entity changes
2. Test bulk payment and single payment functionality
3. Verify payment date operations work correctly
4. Test all payment-related endpoints

### Step 6: Remove payment_date from submission table (FINAL STEP)
**⚠️ WARNING: This is irreversible. Only run after thorough testing!**

```sql
-- Final step: Remove payment_date column from submission table
ALTER TABLE `submission` DROP COLUMN `payment_date`;
```

## Rollback Plan (if needed)
If you need to rollback before Step 6:

```sql
-- Add payment_date back to submission table
ALTER TABLE `submission` ADD COLUMN `payment_date` DATE NULL;

-- Restore payment_date from submission_payment
UPDATE `submission` s
INNER JOIN `submission_payment` sp ON s.`reference_number` = sp.`reference_number`
SET s.`payment_date` = sp.`payment_date`
WHERE sp.`payment_date` IS NOT NULL;
```

## Post-Migration Checklist
- [ ] All submission records have corresponding submission_payment records
- [ ] Payment dates are correctly migrated
- [ ] Application functions correctly with new schema
- [ ] All tests pass
- [ ] Payment update functionality works
- [ ] Excel generation works correctly
- [ ] No data loss occurred

## Notes
- The `task_name` and `current_reviewer` in `submission_payment` are intentionally different from those in `submission` as per your requirements
- Payment dates will be NULL in `submission_payment` for submissions where clients didn't upload payment files or input single payments
- The one-to-one relationship is maintained through the `reference_number` field
