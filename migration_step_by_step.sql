-- =====================================================
-- STEP-BY-STEP MIGRATION FOR DBEAVER
-- Execute each step one by one and verify results
-- =====================================================

-- STEP 1: BACKUP CHECK
-- Before starting, make sure you have a backup!
-- Run this to see current data:
SELECT 'Current submission count' as info, COUNT(*) as count FROM submission
UNION ALL
SELECT 'Current submission_payment count' as info, COUNT(*) as count FROM submission_payment
UNION ALL  
SELECT 'Submissions with payment_date' as info, COUNT(*) as count FROM submission WHERE payment_date IS NOT NULL;

-- STEP 2: CREATE SUBMISSION_PAYMENT TABLE (if not exists)
CREATE TABLE IF NOT EXISTS `submission_payment` (
    `id` BIGINT NOT NULL AUTO_INCREMENT,
    `reference_number` VARCHAR(255) NOT NULL,
    `task_name` VARCHAR(255) NULL,
    `current_reviewer` LONGTEXT NULL,
    `payment_date` DATE NULL,
    `created_at` DATETIME NOT NULL,
    `updated_at` DATETIME NULL,
    `created_by` VARCHAR(255) NULL,
    `updated_by` VARCHAR(255) NULL,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_submission_payment_reference_number` (`reference_number`),
    INDEX `idx_submission_payment_task_name` (`task_name`),
    INDEX `idx_submission_payment_reference_number` (`reference_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- STEP 3: MIGRATE DATA FROM SUBMISSION TO SUBMISSION_PAYMENT
-- This will create submission_payment records ONLY for submissions that have payment_date
-- If client never uploaded/inputted payment data, no record will be created
INSERT INTO `submission_payment` (
    `reference_number`,
    `task_name`,
    `current_reviewer`,
    `payment_date`,
    `created_at`,
    `updated_at`,
    `created_by`,
    `updated_by`
)
SELECT
    s.`reference_number`,
    NULL as `task_name`,  -- Different task_name as per requirement
    NULL as `current_reviewer`,  -- Different current_reviewer as per requirement
    s.`payment_date`,  -- Migrate payment_date from submission
    NOW() as `created_at`,
    NOW() as `updated_at`,
    'MIGRATION_SCRIPT' as `created_by`,
    'MIGRATION_SCRIPT' as `updated_by`
FROM `submission` s
WHERE s.`payment_date` IS NOT NULL  -- Only migrate submissions with actual payment data
  AND s.`reference_number` NOT IN (
    SELECT sp.`reference_number`
    FROM `submission_payment` sp
)
ON DUPLICATE KEY UPDATE
    `payment_date` = VALUES(`payment_date`),
    `updated_at` = NOW(),
    `updated_by` = 'MIGRATION_SCRIPT';

-- STEP 4: VERIFY MIGRATION SUCCESS
-- Check migration results - submission_payment should only contain records with payment data
SELECT
    'Total submissions' as info,
    COUNT(*) as count
FROM submission
UNION ALL
SELECT
    'Submissions with payment_date' as info,
    COUNT(*) as count
FROM submission
WHERE payment_date IS NOT NULL
UNION ALL
SELECT
    'SubmissionPayment records created' as info,
    COUNT(*) as count
FROM submission_payment
UNION ALL
SELECT
    'Submissions without payment data' as info,
    COUNT(*) as count
FROM submission
WHERE payment_date IS NULL;

-- STEP 5: CHECK SUBMISSIONS WITHOUT PAYMENT DATA
-- These submissions should NOT have records in submission_payment (this is expected)
SELECT
    'Submissions without payment data (expected)' as info,
    COUNT(*) as count
FROM submission s
LEFT JOIN submission_payment sp ON s.reference_number = sp.reference_number
WHERE sp.reference_number IS NULL;

-- STEP 6: VERIFY ONLY SUBMISSIONS WITH PAYMENT_DATE HAVE SUBMISSION_PAYMENT RECORDS
-- This should return 0 if migration was correct
SELECT
    'Submissions with NULL payment_date but have submission_payment record (should be 0)' as info,
    COUNT(*) as count
FROM submission s
JOIN submission_payment sp ON s.reference_number = sp.reference_number
WHERE s.payment_date IS NULL;

-- STEP 7: SAMPLE DATA VERIFICATION
-- Check a few records to make sure data migrated correctly
-- This should only show submissions that had payment_date
SELECT
    s.reference_number,
    s.payment_date as original_payment_date,
    sp.payment_date as migrated_payment_date,
    CASE
        WHEN s.payment_date = sp.payment_date
        THEN 'MATCH'
        ELSE 'MISMATCH'
    END as status
FROM submission s
JOIN submission_payment sp ON s.reference_number = sp.reference_number
LIMIT 10;

-- =====================================================
-- AFTER TESTING YOUR APPLICATION WITH THE NEW SCHEMA:
-- =====================================================

-- STEP 8: FINAL STEP - REMOVE PAYMENT_DATE FROM SUBMISSION TABLE
-- ⚠️ WARNING: Only run this after thorough testing!
-- ⚠️ This is irreversible without a backup!
--
-- Uncomment the line below when you're ready:
-- ALTER TABLE `submission` DROP COLUMN `payment_date`;

-- =====================================================
-- ROLLBACK SCRIPT (if needed before Step 7)
-- =====================================================
-- If you need to rollback before removing the column:
-- 
-- -- Restore payment_date to submission from submission_payment
-- UPDATE `submission` s
-- INNER JOIN `submission_payment` sp ON s.`reference_number` = sp.`reference_number`
-- SET s.`payment_date` = sp.`payment_date`
-- WHERE sp.`payment_date` IS NOT NULL;
