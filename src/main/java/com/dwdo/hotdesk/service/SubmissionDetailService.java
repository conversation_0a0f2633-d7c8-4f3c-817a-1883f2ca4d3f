package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.SubmissionDetailDTO;
import com.dwdo.hotdesk.dto.SupportingFileDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.model.SupportingFiles;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.util.NullUtil;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class SubmissionDetailService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionPaymentRepository submissionPaymentRepository;
    private final ApprovalClient approvalClient;

    public SubmissionDetailDTO getSubmissionDetail(Long submissionId) {
        log.info("[SubmissionDetailService] Retrieving submission detail for ID: {}", submissionId);

        Optional<Submission> submissionOpt = submissionRepository.findByIdWithSupportingFiles(submissionId);
        if (submissionOpt.isEmpty()) {
            log.warn("[SubmissionDetailService] Submission not found with ID: {}", submissionId);
            throw CustomBadRequestException.badRequest("Submission not found with ID: " + submissionId);
        }

        Submission submission = submissionOpt.get();

        validateAccess(submission);

        SubmissionDetailDTO submissionDetail = mapToDTO(submission);

        log.info("[SubmissionDetailService] Successfully retrieved submission detail for ID: {}", submissionId);
        return submissionDetail;
    }

    private void validateAccess(Submission submission) {
        String currentUserNIP = SecurityUtil.getCurrentUserLogin().orElse(null);
        List<String> userRoles = SecurityUtil.getCurrentUserRole();

        log.debug("[SubmissionDetailService] Validating access for user: {} with roles: {}", currentUserNIP, userRoles);

        if (userRoles.contains("ROLE_ADMIN_HRPAYROLL") || userRoles.contains("ROLE_ADMIN_HRPAYROLL_HEAD")) {
            log.debug("[SubmissionDetailService] HRPAYROLL/HRPAYROLL_HEAD access granted for user: {}", currentUserNIP);
            return;
        }

        if (userRoles.contains("ROLE_ADMIN_HRBP")) {
            if ("HRBP".equals(submission.getSubmitterJob())) {
                log.debug("[SubmissionDetailService] HRBP access granted for HRBP submission");
                return;
            } else {
                log.warn("[SubmissionDetailService] HRBP user {} attempted to access non-HRBP submission (submitterJob: {})",
                        currentUserNIP, submission.getSubmitterJob());
                throw CustomBadRequestException.badRequest("Access denied. HRBP users can only view HRBP submissions.");
            }
        }

        if (userRoles.contains("ROLE_ADMIN_HRREWARD")) {
            if ("HRREWARD".equals(submission.getSubmitterJob())) {
                log.debug("[SubmissionDetailService] HRREWARD access granted for HRREWARD submission");
                return;
            } else {
                log.warn("[SubmissionDetailService] HRREWARD user {} attempted to access non-HRREWARD submission (submitterJob: {})",
                        currentUserNIP, submission.getSubmitterJob());
                throw CustomBadRequestException.badRequest("Access denied. HRREWARD users can only view HRREWARD submissions.");
            }
        }

        if (currentUserNIP.equals(submission.getCreatedBy())) {
            log.debug("[SubmissionDetailService] Access granted for user: {} to own submission", currentUserNIP);
            return;
        }

        log.warn("[SubmissionDetailService] Access denied. User {} with roles {} attempted to access submission created by {} with submitterJob {}",
                currentUserNIP, userRoles, submission.getCreatedBy(), submission.getSubmitterJob());
        throw CustomBadRequestException.badRequest("Access denied. You can only view submissions based on your role permissions.");
    }


    private SubmissionDetailDTO mapToDTO(Submission submission) {
        String paymentDateDisplay = getPaymentDateDisplay(submission.getReferenceNumber());

        return SubmissionDetailDTO.builder()
                .id(submission.getId())
                .taskName(submission.getTaskName())
                .createdAt(submission.getCreatedAt())
                .referenceNumber(submission.getReferenceNumber())
                .createdBy(submission.getCreatedBy())
                .submitterName(submission.getSubmitterName())
                .submitterJob(submission.getSubmitterJob())
                .status(submission.getStatus())
                .nip(submission.getNip())
                .name(submission.getName())
                .grade(submission.getGrade())
                .paymentType(submission.getPaymentType())
                .amount(submission.getAmount())
                .description(submission.getDescription())
                .monthOfProcess(submission.getMonthOfProcess())
                .yearOfProcess(submission.getYearOfProcess())
                .directorate(submission.getDirectorate())
                .slik(submission.getSlik())
                .sanction(submission.getSanction())
                .terminationDate(submission.getTerminationDate())
                .eligible(submission.getEligible())
                .currentReviewer(NullUtil.toDisplayString(submission.getCurrentReviewer()))
                .paymentDate(paymentDateDisplay)
                .remarks(NullUtil.toDisplayString(submission.getRemarks()))
                .supportingFiles(mapSupportingFiles(submission.getSupportingFiles()))
                .build();
    }

    private String getPaymentDateDisplay(String referenceNumber) {
        Optional<SubmissionPayment> paymentOpt = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
        if (paymentOpt.isPresent() && paymentOpt.get().getPaymentDate() != null) {
            return NullUtil.toDisplayString(paymentOpt.get().getPaymentDate());
        }
        return "-";
    }

    private List<SupportingFileDTO> mapSupportingFiles(List<SupportingFiles> supportingFiles) {
        if (supportingFiles == null || supportingFiles.isEmpty()) {
            return List.of();
        }

        return supportingFiles.stream()
                .map(file -> SupportingFileDTO.builder()
                        .id(file.getId())
                        .fileName(file.getFileName())
                        .build())
                .toList();
    }

    public ApiResponse getTransactionConsoleLog(String taskName) {
        log.info("[SubmissionDetailService] Retrieving transaction console log for task: {}", taskName);

        try {
            ApiResponse response = approvalClient.detail(taskName);

            if (response == null) {
                log.warn("[SubmissionDetailService] Received null response for task: {}", taskName);
                throw CustomBadRequestException.badRequest("No response received from transaction console service for task: " + taskName);
            }

            if (!response.isSuccess()) {
                log.warn("[SubmissionDetailService] Transaction console service returned error for task: {}. Message: {}",
                        taskName, response.getMessage());
                throw CustomBadRequestException.badRequest("Transaction console service error: " + response.getMessage());
            }

            log.info("[SubmissionDetailService] Successfully retrieved transaction console log for task: {}", taskName);
            return response;

        } catch (Exception e) {
            log.error("[SubmissionDetailService] Error retrieving transaction console log for task: {}. Error: {}",
                    taskName, e.getMessage(), e);
            throw CustomBadRequestException.badRequest("Failed to retrieve transaction console log for task: " + taskName + ". Error: " + e.getMessage());
        }
    }
}
