# Payment Update Approval API

## Overview
This document describes the new Payment Update Approval API endpoint that allows initiating approval processes for payment updates.

## Configuration
The following configuration values are used for the payment update approval process:

```properties
rule.code.payment-update=staggered_payment
system.code.payment-update=arjuna
process.name.payment-updatet=Staggered Payment Update
```

## API Endpoint

### Initiate Payment Update Approval

**Endpoint:** `POST /api/staggered/payment-update-approval`

**Content-Type:** `application/json`

**Description:** Initiates an approval process for payment updates. This endpoint creates a new approval workflow for a specific NIP (employee ID).

**Request Body:**
```json
{
  "paymentUpdates": [
    {
      "referenceNumber": "SP-123456789",
      "paymentDate": "2024-01-15"
    },
    {
      "referenceNumber": "SP-987654321",
      "paymentDate": null
    }
  ]
}
```

**Request Body Validation:**
| Field                              | Type                    | Required | Description                                                    |
|------------------------------------|-------------------------|----------|----------------------------------------------------------------|
| `paymentUpdates`                   | Array[PaymentUpdateDTO] | Yes      | List of payment updates to process for approval                |
| `paymentUpdates[].referenceNumber` | String                  | Yes      | Reference number of the submission (must exist in database)   |
| `paymentUpdates[].paymentDate`     | LocalDate               | No       | Payment date (yyyy-MM-dd format). If null, displays as "-"    |

**Success Response:**
- **Status Code:** 200 OK
- **Content-Type:** `application/json`

```json
{
  "code": 200,
  "status": "OK",
  "message": "Payment update approval initiated successfully for 2 records. 0 invalid records.",
  "data": {
    "taskName": "PAYMENT_UPDATE_TASK_ABC123",
    "validCount": 2,
    "invalidCount": 0,
    "details": {
      "SP-123456789": "Valid for approval",
      "SP-987654321": "Valid for approval"
    }
  }
}
```

**Error Responses:**

| Status Code | Description                    | Response Body Example                                                                                    |
|-------------|--------------------------------|----------------------------------------------------------------------------------------------------------|
| 400         | Bad Request - No Valid Records | `{"code": 400, "status": "Error", "message": "No valid payment updates found", "data": {"INVALID_REF": "Not Found: Submission with this reference number does not exist"}}` |
| 400         | Bad Request - Approval Failed  | `{"code": 400, "status": "Error", "message": "Failed to initiate payment update approval"}` |
| 401         | Unauthorized                   | `{"code": 401, "status": "Authentication Error", "message": "User not authenticated or NIP not available"}` |
| 500         | Internal Server Error          | `{"code": 500, "status": "Error", "message": "Internal error occurred while initiating payment update approval: ..."}` |

## Process Flow

1. **Authentication Check**: Verifies that the current user is authenticated
2. **Validation**: Validates each payment update:
   - Checks if reference number is provided
   - Verifies that reference number exists in the submissions table
   - Separates valid and invalid records
3. **Approval Submission**: Uses `ApprovalService.submitToApprovalWithConfig()` to submit the payment update request with:
   - Rule Code: `staggered_payment`
   - System Code: `arjuna`
   - Process Name: `Staggered Payment Update`
   - Parameters: `submitterNip` and `recordCount` (no `isPayroll` parameter needed)
4. **Record Creation**: For each valid payment update, creates or updates a `SubmissionPayment` record with:
   - Reference Number: From the payment update
   - Task Name: Returned from approval service
   - Payment Date: From the payment update (null if empty, which displays as "-")
   - Current Reviewer: Uses `ApprovalService.updateCurrentReviewerForTask()` to extract approver information
5. **Response**: Returns success/failure status with detailed results for each record

## Code Reusability

This implementation reuses existing methods from `ApprovalService`:
- `submitToApprovalWithConfig()` - Handles approval submission with custom configuration
- `updateCurrentReviewerForTask()` - Updates current reviewer information using a functional approach

This eliminates code duplication and ensures consistency across all approval processes.

## Database Impact

The endpoint creates or updates records in the `submission_payment` table with:
- `reference_number`: The reference number from the payment update request
- `task_name`: Task name returned from the approval service
- `current_reviewer`: JSON string containing approver information
- `payment_date`: The payment date from the request (null if not provided, displays as "-")

## Approval Information Structure

The `current_reviewer` field contains a JSON array with approver details:

```json
[
  {
    "approverCode": "APPROVER_001",
    "approverNik": "987654321",
    "approverName": "John Doe",
    "approverJob": "Manager"
  }
]
```

## Usage Examples

### Single Payment Update
```bash
curl -X POST http://localhost:8093/api/staggered/payment-update-approval \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "paymentUpdates": [
      {
        "referenceNumber": "SP-123456789",
        "paymentDate": "2024-01-15"
      }
    ]
  }'
```

### Bulk Payment Updates
```bash
curl -X POST http://localhost:8093/api/staggered/payment-update-approval \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "paymentUpdates": [
      {
        "referenceNumber": "SP-123456789",
        "paymentDate": "2024-01-15"
      },
      {
        "referenceNumber": "SP-987654321",
        "paymentDate": null
      }
    ]
  }'
```

## Notes

- This endpoint initiates the approval process for payment updates
- Supports both single and bulk payment update approvals
- Reference numbers must exist in the submissions table
- Payment dates can be null (displays as "-") or actual dates
- Invalid reference numbers are reported but don't prevent processing of valid ones
- The approval process follows the same pattern as the main staggered payment approval but with different configuration values
- No `isPayroll` parameter is required for payment update approvals
