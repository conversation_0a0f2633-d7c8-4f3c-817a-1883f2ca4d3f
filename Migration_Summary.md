# Database Migration Summary: Submission Payment Table

## Overview
This migration creates a proper separation between submission data and payment data by moving the `payment_date` field from the `submission` table to the `submission_payment` table.

## Current Schema Analysis

### Before Migration:
**Submission Table:**
- Contains `payment_date` field (LocalDate)
- Contains `task_name` and `current_reviewer` fields
- Has one-to-many relationships with files

**SubmissionPayment Table:**
- Already exists with basic structure
- Has `task_name`, `current_reviewer`, and `payment_date` fields
- Has one-to-one relationship with Submission via `reference_number`

### After Migration:
**Submission Table:**
- `payment_date` field removed
- Maintains all other fields
- Added relationship to SubmissionPayment

**SubmissionPayment Table:**
- Contains `payment_date` (migrated from Submission)
- Has separate `task_name` and `current_reviewer` (different from Submission)
- Maintains one-to-one relationship with Submission

## Files Created/Modified

### 1. Database Migration Scripts:
- `database_migration_submission_payment.sql` - Complete migration script
- `migration_step_by_step.sql` - Step-by-step execution for DBeaver
- `DBeaver_Migration_Instructions.md` - Detailed instructions

### 2. Java Code Changes:
- `src/main/java/com/dwdo/hotdesk/repository/SubmissionPaymentRepository.java` - New repository
- `src/main/java/com/dwdo/hotdesk/model/Submission.java` - Removed payment_date field, added relationship

## Key Features of the Migration

### 1. Data Safety:
- Creates submission_payment records ONLY for submissions that have payment_date
- No records created for submissions without payment data (client never uploaded/inputted payment)
- Maintains referential integrity through reference_number

### 2. Separate Task Management:
- `task_name` in SubmissionPayment is independent from Submission.task_name
- `current_reviewer` in SubmissionPayment is independent from Submission.current_reviewer
- This allows different workflow states for payment vs submission

### 3. Conditional Payment Records:
- Records exist in submission_payment ONLY when client provides payment data
- No record = client never uploaded payment file or inputted single payment
- Record exists = client provided payment information

## Migration Steps for DBeaver

1. **Backup Database** - Essential before any migration
2. **Run Step-by-Step Script** - Execute `migration_step_by_step.sql`
3. **Verify Data Integrity** - Check record counts and relationships
4. **Test Application** - Deploy updated code and test functionality
5. **Remove Old Column** - Final step to drop payment_date from submission

## Repository Methods Available

The new `SubmissionPaymentRepository` provides:
- Basic CRUD operations
- Find by reference number, task name, payment date
- Find records with/without payment dates
- Find by current reviewer
- Join queries with submission data
- Data integrity checking methods

## Application Impact

### Services That Need Updates:
- `PaymentUpdateService` - Should update SubmissionPayment instead of Submission
- `GeneratePaymentService` - May need to join with SubmissionPayment for payment dates
- Any service that reads/writes payment_date

### Controllers:
- Payment-related endpoints may need to handle SubmissionPayment operations

### DTOs:
- Consider creating SubmissionPaymentDTO if needed for API responses

## Testing Checklist

- [ ] All existing submissions have corresponding submission_payment records
- [ ] Payment dates are correctly migrated
- [ ] Bulk payment upload works
- [ ] Single payment submission works  
- [ ] Payment status updates work
- [ ] Excel generation includes payment dates correctly
- [ ] API endpoints return correct payment information

## Rollback Plan

If issues occur before the final column drop:
1. Update submission table with payment_date from submission_payment
2. Revert application code changes
3. Drop submission_payment table if needed

## Benefits

1. **Separation of Concerns**: Payment data is separate from submission data
2. **Flexible Workflow**: Different task names and reviewers for payment vs submission
3. **Data Integrity**: One-to-one relationship ensures consistency
4. **Null Handling**: Properly handles cases where payment files aren't uploaded
5. **Scalability**: Easier to extend payment-related features in the future

## Notes

- The migration preserves all existing data
- Payment dates will be NULL for submissions without payment information
- The relationship is maintained through reference_number (not foreign key by default)
- Task names and reviewers in SubmissionPayment are intentionally separate from Submission
