package com.dwdo.hotdesk.model;

import lombok.*;
import com.dwdo.hotdesk.model.audit.DateAudit;
import javax.persistence.*;
import java.time.LocalDate;

@Entity
@Table(name = "submission_payment",
    indexes = {
        @Index(name="idx_submission_payment_task_name", columnList = "task_name"),
        @Index(name="idx_submission_payment_reference_number", columnList = "reference_number")
    },
    uniqueConstraints = {
        @UniqueConstraint(name = "uk_submission_payment_reference_number",
                         columnNames = {"reference_number"})
    }
)
@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SubmissionPayment extends DateAudit {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "reference_number", nullable = false, unique = true)
    private String referenceNumber;

    @Column(name = "task_name")
    private String taskName;

    @Column(columnDefinition = "LONGTEXT", name = "current_reviewer")
    private String currentReviewer;

    @Column(name = "payment_date")
    private LocalDate paymentDate;

    @OneToOne
    @JoinColumn(name = "reference_number", referencedColumnName = "reference_number", insertable = false, updatable = false)
    private Submission submission;
}
