package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentUpdateDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.time.LocalDate;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("Payment Update Service Approval Tests")
class PaymentUpdateServiceApprovalTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private SubmissionPaymentRepository submissionPaymentRepository;

    @Mock
    private ApprovalService approvalService;

    @InjectMocks
    private PaymentUpdateService paymentUpdateService;

    private static final String TEST_REFERENCE_NUMBER = "SP-123456789";
    private static final String TEST_TASK_NAME = "PAYMENT_UPDATE_TASK_123";

    @BeforeEach
    void setUp() {
        // Set up configuration values
        ReflectionTestUtils.setField(paymentUpdateService, "paymentUpdateRuleCode", "staggered_payment");
        ReflectionTestUtils.setField(paymentUpdateService, "paymentUpdateSystemCode", "arjuna");
        ReflectionTestUtils.setField(paymentUpdateService, "paymentUpdateProcessName", "Staggered Payment Update");
    }

    @Test
    @DisplayName("Should successfully initiate payment update approval")
    void shouldSuccessfullyInitiatePaymentUpdateApproval() {
        // Given
        List<PaymentUpdateDTO> paymentUpdates = Arrays.asList(
                new PaymentUpdateDTO(TEST_REFERENCE_NUMBER, LocalDate.now()),
                new PaymentUpdateDTO("SP-987654321", null)
        );

        Submission mockSubmission1 = new Submission();
        mockSubmission1.setReferenceNumber(TEST_REFERENCE_NUMBER);
        Submission mockSubmission2 = new Submission();
        mockSubmission2.setReferenceNumber("SP-987654321");

        when(submissionRepository.findByReferenceNumber(TEST_REFERENCE_NUMBER)).thenReturn(Optional.of(mockSubmission1));
        when(submissionRepository.findByReferenceNumber("SP-987654321")).thenReturn(Optional.of(mockSubmission2));
        when(approvalService.submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(TEST_TASK_NAME);
        when(submissionPaymentRepository.findByReferenceNumber(anyString())).thenReturn(Optional.empty());
        when(submissionPaymentRepository.save(any(SubmissionPayment.class))).thenReturn(new SubmissionPayment());

        // When
        GeneralBodyResponse response = paymentUpdateService.initiatePaymentUpdateApproval(paymentUpdates);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());
        assertTrue(response.getMessage().contains("Payment update approval initiated successfully"));

        verify(approvalService).submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any());
        verify(approvalService, times(2)).updateCurrentReviewerForTask(eq(TEST_TASK_NAME), anyString(), any());
        verify(submissionPaymentRepository, times(2)).save(any(SubmissionPayment.class));
    }

    @Test
    @DisplayName("Should handle approval submission failure")
    void shouldHandleApprovalSubmissionFailure() {
        // Given
        List<PaymentUpdateDTO> paymentUpdates = Arrays.asList(
                new PaymentUpdateDTO(TEST_REFERENCE_NUMBER, LocalDate.now())
        );

        Submission mockSubmission = new Submission();
        mockSubmission.setReferenceNumber(TEST_REFERENCE_NUMBER);

        when(submissionRepository.findByReferenceNumber(TEST_REFERENCE_NUMBER)).thenReturn(Optional.of(mockSubmission));
        when(approvalService.submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(null);

        // When
        GeneralBodyResponse response = paymentUpdateService.initiatePaymentUpdateApproval(paymentUpdates);

        // Then
        assertNotNull(response);
        assertEquals(400, response.getCode());
        assertEquals("Error", response.getStatus());
        assertTrue(response.getMessage().contains("Failed to initiate payment update approval"));

        verify(approvalService).submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any());
        verify(submissionPaymentRepository, never()).save(any());
    }

    @Test
    @DisplayName("Should handle invalid reference numbers")
    void shouldHandleInvalidReferenceNumbers() {
        // Given
        List<PaymentUpdateDTO> paymentUpdates = Arrays.asList(
                new PaymentUpdateDTO(TEST_REFERENCE_NUMBER, LocalDate.now()),
                new PaymentUpdateDTO("INVALID_REF", null)
        );

        Submission mockSubmission = new Submission();
        mockSubmission.setReferenceNumber(TEST_REFERENCE_NUMBER);

        when(submissionRepository.findByReferenceNumber(TEST_REFERENCE_NUMBER)).thenReturn(Optional.of(mockSubmission));
        when(submissionRepository.findByReferenceNumber("INVALID_REF")).thenReturn(Optional.empty());
        when(approvalService.submitToApprovalWithConfig(anyString(), anyString(), anyString(), anyString(), anyString(), any()))
                .thenReturn(TEST_TASK_NAME);
        when(submissionPaymentRepository.findByReferenceNumber(TEST_REFERENCE_NUMBER)).thenReturn(Optional.empty());
        when(submissionPaymentRepository.save(any(SubmissionPayment.class))).thenReturn(new SubmissionPayment());

        // When
        GeneralBodyResponse response = paymentUpdateService.initiatePaymentUpdateApproval(paymentUpdates);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());
        assertTrue(response.getMessage().contains("1 invalid records"));

        // Only one valid record should be processed
        verify(submissionPaymentRepository, times(1)).save(any(SubmissionPayment.class));
        verify(approvalService).updateCurrentReviewerForTask(eq(TEST_TASK_NAME), anyString(), any());
    }
}
