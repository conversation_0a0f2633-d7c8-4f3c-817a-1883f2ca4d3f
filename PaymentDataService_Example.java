package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.model.SubmissionPayment;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.repository.SubmissionPaymentRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Optional;

/**
 * Example service showing how to work with the new SubmissionPayment approach
 * where records only exist when client provides payment data
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PaymentDataService {

    private final PaymentSubmissionRepository submissionRepository;
    private final SubmissionPaymentRepository submissionPaymentRepository;

    /**
     * Create payment data when client uploads payment file or inputs single payment
     */
    public SubmissionPayment createPaymentData(String referenceNumber, LocalDate paymentDate, 
                                             String taskName, String currentReviewer) {
        log.info("[PaymentDataService] Creating payment data for reference: {}", referenceNumber);
        
        // Check if submission exists
        Optional<Submission> submissionOpt = submissionRepository.findByReferenceNumber(referenceNumber);
        if (submissionOpt.isEmpty()) {
            throw new RuntimeException("Submission not found: " + referenceNumber);
        }
        
        // Create or update submission payment record
        Optional<SubmissionPayment> existingPayment = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
        
        SubmissionPayment submissionPayment;
        if (existingPayment.isPresent()) {
            // Update existing payment data
            submissionPayment = existingPayment.get();
            submissionPayment.setPaymentDate(paymentDate);
            submissionPayment.setTaskName(taskName);
            submissionPayment.setCurrentReviewer(currentReviewer);
            log.info("[PaymentDataService] Updated existing payment data for reference: {}", referenceNumber);
        } else {
            // Create new payment data
            submissionPayment = SubmissionPayment.builder()
                    .referenceNumber(referenceNumber)
                    .paymentDate(paymentDate)
                    .taskName(taskName)
                    .currentReviewer(currentReviewer)
                    .build();
            log.info("[PaymentDataService] Created new payment data for reference: {}", referenceNumber);
        }
        
        return submissionPaymentRepository.save(submissionPayment);
    }

    /**
     * Get payment date for a submission (returns null if no payment data exists)
     */
    public LocalDate getPaymentDate(String referenceNumber) {
        Optional<SubmissionPayment> paymentOpt = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
        if (paymentOpt.isPresent()) {
            return paymentOpt.get().getPaymentDate();
        }
        log.debug("[PaymentDataService] No payment data found for reference: {}", referenceNumber);
        return null; // Client never provided payment data
    }

    /**
     * Check if submission has payment data
     */
    public boolean hasPaymentData(String referenceNumber) {
        return submissionPaymentRepository.existsByReferenceNumber(referenceNumber);
    }

    /**
     * Update payment date (only if payment data already exists)
     */
    public boolean updatePaymentDate(String referenceNumber, LocalDate newPaymentDate) {
        Optional<SubmissionPayment> paymentOpt = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
        if (paymentOpt.isPresent()) {
            SubmissionPayment payment = paymentOpt.get();
            payment.setPaymentDate(newPaymentDate);
            submissionPaymentRepository.save(payment);
            log.info("[PaymentDataService] Updated payment date for reference: {}", referenceNumber);
            return true;
        }
        log.warn("[PaymentDataService] Cannot update payment date - no payment data exists for reference: {}", referenceNumber);
        return false; // No payment data exists, client never provided payment info
    }

    /**
     * Remove payment data (when client removes payment file)
     */
    public void removePaymentData(String referenceNumber) {
        Optional<SubmissionPayment> paymentOpt = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
        if (paymentOpt.isPresent()) {
            submissionPaymentRepository.delete(paymentOpt.get());
            log.info("[PaymentDataService] Removed payment data for reference: {}", referenceNumber);
        } else {
            log.debug("[PaymentDataService] No payment data to remove for reference: {}", referenceNumber);
        }
    }

    /**
     * Get submission with payment data (if exists)
     */
    public Optional<SubmissionPayment> getSubmissionWithPaymentData(String referenceNumber) {
        return submissionPaymentRepository.findByReferenceNumberWithSubmission(referenceNumber);
    }

    /**
     * Check if submission should show "Payment Pending" status
     */
    public boolean isPaymentPending(String referenceNumber) {
        // If no payment data exists, payment is pending (client hasn't provided payment info)
        return !submissionPaymentRepository.existsByReferenceNumber(referenceNumber);
    }
}
