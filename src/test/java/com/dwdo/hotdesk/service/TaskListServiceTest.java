package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.TaskListDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.util.NullUtil;
import com.dwdo.hotdesk.security.SecurityUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for TaskListService
 * Tests approval task list retrieval with various filtering options and current user filtering
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Task List Service Tests")
@SuppressWarnings("unchecked")
class TaskListServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private SubmissionFilterService filterService;

    @InjectMocks
    private TaskListService taskListService;

    private Submission mockSubmission;
    private Pageable mockPageable;
    private String currentUserNik;

    @BeforeEach
    void setUp() {
        mockSubmission = createMockSubmission();
        mockPageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
        currentUserNik = "reviewer123";
    }

    @Nested
    @DisplayName("Get Approval Task List Tests")
    class GetApprovalTaskListTests {

        @Test
        @DisplayName("Should successfully retrieve approval task list with default parameters")
        void testGetApprovalTaskList_Success_DefaultParams() {
            // Given
            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(submissionPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getContent()).hasSize(1);
                assertThat(result.getTotalElements()).isEqualTo(1);

                TaskListDTO dto = result.getContent().get(0);
                assertThat(dto.getId()).isEqualTo(mockSubmission.getId());
                assertThat(dto.getTaskName()).isEqualTo(mockSubmission.getTaskName());
                assertThat(dto.getStatus()).isEqualTo(mockSubmission.getStatus());
                assertThat(dto.getNip()).isEqualTo(mockSubmission.getNip());
                assertThat(dto.getName()).isEqualTo(mockSubmission.getName());
                assertThat(dto.getSubmitterName()).isEqualTo(mockSubmission.getSubmitterName());
                assertThat(dto.getAmount()).isEqualTo(mockSubmission.getAmount());

                verify(filterService, times(1)).parseStartDate(isNull());
                verify(filterService, times(1)).parseEndDate(isNull());
                verify(filterService, times(1)).buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull());
                verify(submissionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
            }
        }

        @Test
        @DisplayName("Should successfully retrieve approval task list with filtering parameters")
        void testGetApprovalTaskList_Success_WithFilters() {
            // Given
            String status = "PENDING";
            String startDateStr = "2024-01-01";
            String endDateStr = "2024-12-31";
            String nip = "123456789";
            String name = "John";
            
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(startDateStr)).thenReturn(startDate);
            when(filterService.parseEndDate(endDateStr)).thenReturn(endDate);
            when(filterService.buildFilterSpecification(isNull(), eq(status), eq(startDate), eq(endDate), eq(nip), eq(name), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(submissionPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, status, startDateStr, endDateStr, nip, name, null);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getContent()).hasSize(1);

                verify(filterService, times(1)).parseStartDate(startDateStr);
                verify(filterService, times(1)).parseEndDate(endDateStr);
                verify(filterService, times(1)).buildFilterSpecification(isNull(), eq(status), eq(startDate), eq(endDate), eq(nip), eq(name), isNull());
                verify(submissionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
            }
        }

        @Test
        @DisplayName("Should filter tasks by current reviewer (current user)")
        void testGetApprovalTaskList_Success_CurrentReviewerFilter() {
            // Given
            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(submissionPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getContent()).hasSize(1);

                // Verify that the specification includes current reviewer filter
                verify(submissionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
            }
        }

        @Test
        @DisplayName("Should handle empty task list results")
        void testGetApprovalTaskList_EmptyResults() {
            // Given
            Page<Submission> emptyPage = new PageImpl<>(Arrays.asList(), mockPageable, 0);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(emptyPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getContent()).isEmpty();
                assertThat(result.getTotalElements()).isEqualTo(0);

                verify(filterService, times(1)).buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull());
                verify(submissionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
            }
        }

        @Test
        @DisplayName("Should throw exception when current user not found")
        void testGetApprovalTaskList_CurrentUserNotFound() {
            // When & Then
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.empty());

                assertThatThrownBy(() -> taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null))
                        .isInstanceOf(RuntimeException.class)
                        .hasMessageContaining("Current user not found");
            }
        }

        @Test
        @DisplayName("Should handle filter service exception")
        void testGetApprovalTaskList_FilterServiceException() {
            // Given
            when(filterService.parseStartDate(anyString()))
                    .thenThrow(new RuntimeException("Invalid date format"));

            // When & Then
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                assertThatThrownBy(() -> taskListService.getApprovalTaskList(
                        mockPageable, null, "invalid-date", null, null, null, null))
                        .isInstanceOf(RuntimeException.class)
                        .hasMessageContaining("Invalid date format");

                verify(filterService, times(1)).parseStartDate("invalid-date");
            }
        }

        @Test
        @DisplayName("Should handle repository exception")
        void testGetApprovalTaskList_RepositoryException() {
            // Given
            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                assertThatThrownBy(() -> taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null))
                        .isInstanceOf(RuntimeException.class)
                        .hasMessageContaining("Database connection failed");

                verify(submissionRepository, times(1)).findAll(any(Specification.class), any(Pageable.class));
            }
        }
    }

    @Nested
    @DisplayName("Mapping Tests")
    class MappingTests {

        @Test
        @DisplayName("Should correctly map Submission to TaskListDTO")
        void testMapToTaskListDTO_Success() {
            // Given
            Submission submission = createMockSubmission();
            List<Submission> submissions = Arrays.asList(submission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(submissionPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null);

                // Then
                assertThat(result.getContent()).hasSize(1);
                TaskListDTO dto = result.getContent().get(0);

                // Verify all fields are correctly mapped
                assertThat(dto.getId()).isEqualTo(submission.getId());
                assertThat(dto.getTaskName()).isEqualTo(submission.getTaskName());
                assertThat(dto.getReferenceNumber()).isEqualTo(submission.getReferenceNumber());
                assertThat(dto.getSubmitterName()).isEqualTo(submission.getSubmitterName());
                assertThat(dto.getSubmitterJob()).isEqualTo(submission.getSubmitterJob());
                assertThat(dto.getStatus()).isEqualTo(submission.getStatus());
                assertThat(dto.getNip()).isEqualTo(submission.getNip());
                assertThat(dto.getName()).isEqualTo(submission.getName());
                assertThat(dto.getGrade()).isEqualTo(submission.getGrade());
                assertThat(dto.getPaymentType()).isEqualTo(submission.getPaymentType());
                assertThat(dto.getAmount()).isEqualTo(submission.getAmount());
                assertThat(dto.getDescription()).isEqualTo(submission.getDescription());
                assertThat(dto.getMonthOfProcess()).isEqualTo(submission.getMonthOfProcess());
                assertThat(dto.getYearOfProcess()).isEqualTo(submission.getYearOfProcess());
                assertThat(dto.getDirectorate()).isEqualTo(submission.getDirectorate());
                assertThat(dto.getSlik()).isEqualTo(submission.getSlik());
                assertThat(dto.getSanction()).isEqualTo(submission.getSanction());
                assertThat(dto.getTerminationDate()).isEqualTo(submission.getTerminationDate());
                assertThat(dto.getEligible()).isEqualTo(submission.getEligible());
                assertThat(dto.getPaymentDate()).isEqualTo(NullUtil.toDisplayString(submission.getPaymentDate()));
                assertThat(dto.getRemarks()).isEqualTo(NullUtil.toDisplayString(submission.getRemarks()));
            }
        }

        @Test
        @DisplayName("Should handle mapping with null values")
        void testMapToTaskListDTO_WithNullValues() {
            // Given
            Submission submissionWithNulls = new Submission();
            submissionWithNulls.setId(1L);
            submissionWithNulls.setTaskName("TASK-001");
            submissionWithNulls.setStatus("PENDING");
            // Leave other fields as null

            List<Submission> submissions = Arrays.asList(submissionWithNulls);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(submissionPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null);

                // Then
                assertThat(result.getContent()).hasSize(1);
                TaskListDTO dto = result.getContent().get(0);

                assertThat(dto.getId()).isEqualTo(1L);
                assertThat(dto.getTaskName()).isEqualTo("TASK-001");
                assertThat(dto.getStatus()).isEqualTo("PENDING");
                assertThat(dto.getReferenceNumber()).isNull();
                assertThat(dto.getSubmitterName()).isNull();
                assertThat(dto.getAmount()).isNull();
                assertThat(dto.getEligible()).isNull();
            }
        }

        @Test
        @DisplayName("Should handle mapping with multiple submissions")
        void testMapToTaskListDTO_MultipleSubmissions() {
            // Given
            Submission submission1 = createMockSubmission();
            Submission submission2 = createMockSubmission();
            submission2.setId(2L);
            submission2.setTaskName("TASK-002");
            submission2.setStatus("APPROVED");
            submission2.setNip("987654321");
            submission2.setName("Jane Smith");

            List<Submission> submissions = Arrays.asList(submission1, submission2);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 2);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.buildFilterSpecification(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull()))
                    .thenReturn(Specification.<Submission>where(null));
            when(submissionRepository.findAll(any(Specification.class), any(Pageable.class)))
                    .thenReturn(submissionPage);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNik));

                Page<TaskListDTO> result = taskListService.getApprovalTaskList(
                        mockPageable, null, null, null, null, null, null);

                // Then
                assertThat(result.getContent()).hasSize(2);
                assertThat(result.getTotalElements()).isEqualTo(2);

                TaskListDTO dto1 = result.getContent().get(0);
                TaskListDTO dto2 = result.getContent().get(1);

                assertThat(dto1.getId()).isEqualTo(1L);
                assertThat(dto1.getTaskName()).isEqualTo("TASK-001");
                assertThat(dto1.getStatus()).isEqualTo("PENDING");

                assertThat(dto2.getId()).isEqualTo(2L);
                assertThat(dto2.getTaskName()).isEqualTo("TASK-002");
                assertThat(dto2.getStatus()).isEqualTo("APPROVED");
            }
        }
    }

    /**
     * Helper method to create a mock Submission entity
     */
    private Submission createMockSubmission() {
        Submission submission = new Submission();
        submission.setId(1L);
        submission.setTaskName("TASK-001");
        submission.setReferenceNumber("REF-001");
        submission.setSubmitterName("John Doe");
        submission.setSubmitterJob("Developer");
        submission.setStatus("PENDING");
        submission.setNip("123456789");
        submission.setName("John Doe");
        submission.setGrade("A");
        submission.setPaymentType("Salary");
        submission.setAmount(new BigDecimal("5000000"));
        submission.setDescription("Monthly salary");
        submission.setMonthOfProcess("January");
        submission.setYearOfProcess("2024");
        submission.setDirectorate("IT");
        submission.setSlik("-");
        submission.setSanction("-");
        submission.setTerminationDate("-");
        submission.setEligible(true);
        submission.setCurrentReviewer("reviewer123,reviewer456");
        return submission;
    }
}
