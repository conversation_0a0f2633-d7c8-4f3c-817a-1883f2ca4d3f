# Services Updated for SubmissionPayment Migration

## Overview
All services that previously used `payment_date` from the `Submission` entity have been updated to use the new `SubmissionPayment` table approach where records only exist when clients provide payment data.

## Updated Services

### 1. PaymentUpdateService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/PaymentUpdateService.java`

**Changes Made**:
- Added `SubmissionPaymentRepository` dependency
- Updated `updateSubmission()` method to create/update/delete records in `SubmissionPayment` table
- Added `createOrUpdatePaymentData()` method to handle payment data creation
- Added `removePaymentData()` method to delete payment records when payment_date is null

**Key Logic**:
- When `paymentDate != null`: Creates or updates `SubmissionPayment` record
- When `paymentDate == null`: Removes `SubmissionPayment` record (client removed payment data)
- Submission status is updated to "Paid" or "Unpaid" accordingly

### 2. SubmissionDetailService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/SubmissionDetailService.java`

**Changes Made**:
- Added `SubmissionPaymentRepository` dependency
- Updated `mapToDTO()` method to get payment date from `SubmissionPayment` table
- Added `getPaymentDateDisplay()` helper method

**Key Logic**:
- Looks up payment data in `SubmissionPayment` table by reference number
- Returns "-" if no payment data exists (client never provided payment info)
- Returns formatted date if payment data exists

### 3. TaskListService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/TaskListService.java`

**Changes Made**:
- Added `SubmissionPaymentRepository` dependency
- Updated `mapToTaskListDTO()` method to get payment date from `SubmissionPayment` table
- Added `getPaymentDateDisplay()` helper method

**Key Logic**:
- Same approach as SubmissionDetailService
- Returns "-" for submissions without payment data

### 4. SubmissionHistoryService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/SubmissionHistoryService.java`

**Changes Made**:
- Added `SubmissionPaymentRepository` dependency
- Updated `mapToDTO()` method to get payment date from `SubmissionPayment` table
- Added `getPaymentDateDisplay()` helper method

**Key Logic**:
- Same approach as other services
- Maintains backward compatibility in API responses

### 5. ReportService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/ReportService.java`

**Changes Made**:
- Added `SubmissionPaymentRepository` dependency
- Updated `mapSubmissionToReportData()` method to get payment date from `SubmissionPayment` table
- Added `getPaymentDateDisplay()` helper method
- Added `NullUtil` import

**Key Logic**:
- Excel reports now show payment dates from `SubmissionPayment` table
- Shows "-" for submissions without payment data

## Services That Don't Need Updates

### GeneratePaymentService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/GeneratePaymentService.java`
- **No changes needed** - This service generates Excel files based on submission data only
- Doesn't use payment_date field directly

### SubmissionService ✅
**File**: `src/main/java/com/dwdo/hotdesk/service/SubmissionService.java`
- **No changes needed** - Creates submissions without setting payment_date
- Payment data is created separately when clients upload payment files

## Common Pattern Used

All updated services follow this pattern:

```java
private String getPaymentDateDisplay(String referenceNumber) {
    Optional<SubmissionPayment> paymentOpt = submissionPaymentRepository.findByReferenceNumber(referenceNumber);
    if (paymentOpt.isPresent() && paymentOpt.get().getPaymentDate() != null) {
        return NullUtil.toDisplayString(paymentOpt.get().getPaymentDate());
    }
    return "-"; // No payment data provided by client
}
```

## Key Benefits

1. **Conditional Records**: Only creates `SubmissionPayment` records when clients provide payment data
2. **Clear Indication**: Absence of record = client never provided payment information
3. **Backward Compatibility**: All DTOs still return payment date information (or "-")
4. **Consistent Display**: Uses "-" to indicate no payment data across all services

## Testing Considerations

After migration, test these scenarios:

1. **Submissions with payment data**: Should show actual payment dates
2. **Submissions without payment data**: Should show "-" in all APIs
3. **Payment updates**: Should create/update/delete `SubmissionPayment` records correctly
4. **Excel reports**: Should include correct payment date information
5. **Task lists**: Should display payment dates correctly

## Files Modified

- `PaymentUpdateService.java` - Core payment update logic
- `SubmissionDetailService.java` - Submission detail API
- `TaskListService.java` - Task list API  
- `SubmissionHistoryService.java` - Submission history API
- `ReportService.java` - Excel report generation

All services now properly handle the new `SubmissionPayment` table structure where records only exist when clients provide payment information.
