package com.dwdo.hotdesk.repository;

import com.dwdo.hotdesk.model.SubmissionPayment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@Repository
public interface SubmissionPaymentRepository extends JpaRepository<SubmissionPayment, Long>, JpaSpecificationExecutor<SubmissionPayment> {
    
    /**
     * Find SubmissionPayment by reference number
     * Returns empty if client never uploaded/inputted payment data
     */
    Optional<SubmissionPayment> findByReferenceNumber(String referenceNumber);

    /**
     * Find SubmissionPayment by task name
     */
    Optional<SubmissionPayment> findByTaskName(String taskName);

    /**
     * Find SubmissionPayments by task names
     */
    List<SubmissionPayment> findByTaskNameIn(List<String> taskNames);

    /**
     * Check if SubmissionPayment exists by reference number
     * Returns false if client never uploaded/inputted payment data
     */
    boolean existsByReferenceNumber(String referenceNumber);

    /**
     * Find all SubmissionPayments (only records where client provided payment data)
     */
    List<SubmissionPayment> findAll();

    /**
     * Find SubmissionPayments by payment date
     */
    List<SubmissionPayment> findByPaymentDate(LocalDate paymentDate);

    /**
     * Find SubmissionPayments by payment date range
     */
    List<SubmissionPayment> findByPaymentDateBetween(LocalDate startDate, LocalDate endDate);

    /**
     * Find SubmissionPayments by current reviewer
     */
    List<SubmissionPayment> findByCurrentReviewer(String currentReviewer);

    /**
     * Find SubmissionPayments by current reviewer containing text
     */
    List<SubmissionPayment> findByCurrentReviewerContaining(String reviewerText);
    
    /**
     * Count SubmissionPayments by reference number
     */
    long countByReferenceNumber(String referenceNumber);

}
