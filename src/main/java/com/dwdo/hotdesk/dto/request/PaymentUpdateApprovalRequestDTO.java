package com.dwdo.hotdesk.dto.request;

import com.dwdo.hotdesk.dto.PaymentUpdateDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class PaymentUpdateApprovalRequestDTO {

    @NotEmpty(message = "Payment updates list cannot be empty")
    @Valid
    private List<PaymentUpdateDTO> paymentUpdates;
}
