package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.SubmissionHistoryDTO;
import com.dwdo.hotdesk.model.Submission;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.security.SecurityUtil;
import com.dwdo.hotdesk.service.feign.ApprovalClient;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.dwdo.hotdesk.service.feign.response.DetailResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Comprehensive unit tests for SubmissionHistoryService
 * Tests submission history retrieval with various filtering options and approval details integration
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Submission History Service Tests")
class SubmissionHistoryServiceTest {

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @Mock
    private SubmissionFilterService filterService;

    @Mock
    private ApprovalClient approvalClient;

    @InjectMocks
    private SubmissionHistoryService submissionHistoryService;

    private Submission mockSubmission;
    private Pageable mockPageable;

    @BeforeEach
    void setUp() {
        mockSubmission = createMockSubmission();
        mockPageable = PageRequest.of(0, 10, Sort.by(Sort.Direction.DESC, "id"));
    }

    @Nested
    @DisplayName("Get Submission History Tests")
    class GetSubmissionHistoryTests {

        @Test
        @DisplayName("Should successfully retrieve submission history with default parameters")
        void testGetSubmissionHistory_Success_DefaultParams() {
            // Given
            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.fetchSubmissions(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class)))
                    .thenReturn(submissionPage);

            ApiResponse mockApiResponse = createMockApiResponse();
            when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

            // When
            Page<SubmissionHistoryDTO> result = submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    mockPageable, null, null, null, false, null, null, null);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);
            assertThat(result.getTotalElements()).isEqualTo(1);

            SubmissionHistoryDTO dto = result.getContent().get(0);
            assertThat(dto.getId()).isEqualTo(mockSubmission.getId());
            assertThat(dto.getTaskName()).isEqualTo(mockSubmission.getTaskName());
            assertThat(dto.getStatus()).isEqualTo(mockSubmission.getStatus());
            assertThat(dto.getNip()).isEqualTo(mockSubmission.getNip());
            assertThat(dto.getName()).isEqualTo(mockSubmission.getName());

            verify(filterService, times(1)).parseStartDate(isNull());
            verify(filterService, times(1)).parseEndDate(isNull());
            verify(filterService, times(1)).fetchSubmissions(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class));
        }

        @Test
        @DisplayName("Should successfully retrieve submission history with filtering parameters")
        void testGetSubmissionHistory_Success_WithFilters() {
            // Given
            String status = "PENDING";
            String startDateStr = "2024-01-01";
            String endDateStr = "2024-12-31";
            String nip = "123456789";
            String name = "John";
            
            LocalDateTime startDate = LocalDateTime.of(2024, 1, 1, 0, 0);
            LocalDateTime endDate = LocalDateTime.of(2024, 12, 31, 23, 59);

            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(startDateStr)).thenReturn(startDate);
            when(filterService.parseEndDate(endDateStr)).thenReturn(endDate);
            when(filterService.fetchSubmissions(isNull(), eq(status), eq(startDate), eq(endDate), eq(nip), eq(name), isNull(), any(Pageable.class)))
                    .thenReturn(submissionPage);

            ApiResponse mockApiResponse = createMockApiResponse();
            when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

            // When
            Page<SubmissionHistoryDTO> result = submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    mockPageable, status, startDateStr, endDateStr, false, nip, name, null);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).hasSize(1);

            verify(filterService, times(1)).parseStartDate(startDateStr);
            verify(filterService, times(1)).parseEndDate(endDateStr);
            verify(filterService, times(1)).fetchSubmissions(isNull(), eq(status), eq(startDate), eq(endDate), eq(nip), eq(name), isNull(), any(Pageable.class));
        }

        @Test
        @DisplayName("Should filter by current user when currentUserOnly is true")
        void testGetSubmissionHistory_Success_CurrentUserOnly() {
            // Given
            String currentUserNIP = "current-user-123";
            List<Submission> submissions = Arrays.asList(mockSubmission);
            Page<Submission> submissionPage = new PageImpl<>(submissions, mockPageable, 1);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.fetchSubmissions(eq(currentUserNIP), isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class)))
                    .thenReturn(submissionPage);

            ApiResponse mockApiResponse = createMockApiResponse();
            when(approvalClient.detail(anyString())).thenReturn(mockApiResponse);

            // When
            try (MockedStatic<SecurityUtil> mockedSecurityUtil = mockStatic(SecurityUtil.class)) {
                mockedSecurityUtil.when(SecurityUtil::getCurrentUserLogin).thenReturn(Optional.of(currentUserNIP));

                Page<SubmissionHistoryDTO> result = submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                        mockPageable, null, null, null, true, null, null, null);

                // Then
                assertThat(result).isNotNull();
                assertThat(result.getContent()).hasSize(1);

                verify(filterService, times(1)).fetchSubmissions(eq(currentUserNIP), isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class));
            }
        }

        @Test
        @DisplayName("Should handle empty results")
        void testGetSubmissionHistory_EmptyResults() {
            // Given
            Page<Submission> emptyPage = new PageImpl<>(Arrays.asList(), mockPageable, 0);

            when(filterService.parseStartDate(isNull())).thenReturn(null);
            when(filterService.parseEndDate(isNull())).thenReturn(null);
            when(filterService.fetchSubmissions(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class)))
                    .thenReturn(emptyPage);

            // When
            Page<SubmissionHistoryDTO> result = submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    mockPageable, null, null, null, false, null, null, null);

            // Then
            assertThat(result).isNotNull();
            assertThat(result.getContent()).isEmpty();
            assertThat(result.getTotalElements()).isEqualTo(0);

            verify(filterService, times(1)).fetchSubmissions(isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), isNull(), any(Pageable.class));
        }

        @Test
        @DisplayName("Should handle filter service exception")
        void testGetSubmissionHistory_FilterServiceException() {
            // Given
            when(filterService.parseStartDate(isNull())).thenThrow(new CustomBadRequestException(400, "Invalid Date", "Invalid date format"));

            // When & Then
            assertThatThrownBy(() -> submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    mockPageable, null, null, null, false, null, null, null))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Invalid date format");

            verify(filterService, times(1)).parseStartDate(isNull());
        }

        @Test
        @DisplayName("Should handle general exception and wrap in CustomBadRequestException")
        void testGetSubmissionHistory_GeneralException() {
            // Given
            when(filterService.parseStartDate(isNull())).thenThrow(new RuntimeException("Database connection failed"));

            // When & Then
            assertThatThrownBy(() -> submissionHistoryService.getSubmissionHistoryWithApprovalDetails(
                    mockPageable, null, null, null, false, null, null, null))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Error retrieving submission history");

            verify(filterService, times(1)).parseStartDate(isNull());
        }
    }

    @Nested
    @DisplayName("Update Submission with Approval Details Tests")
    class UpdateSubmissionWithApprovalDetailsTests {

        @Test
        @DisplayName("Should successfully update submission with approval details")
        void testUpdateSubmissionWithApprovalDetails_Success() {
            // Given
            ApiResponse mockApiResponse = createMockApiResponse();
            when(approvalClient.detail(mockSubmission.getTaskName())).thenReturn(mockApiResponse);

            // When
            submissionHistoryService.updateSubmissionWithApprovalDetails(mockSubmission);

            // Then
            verify(approvalClient, times(1)).detail(mockSubmission.getTaskName());
            assertThat(mockSubmission.getCurrentReviewer()).isEqualTo("");
        }

        @Test
        @DisplayName("Should handle null response from approval client")
        void testUpdateSubmissionWithApprovalDetails_NullResponse() {
            // Given
            when(approvalClient.detail(mockSubmission.getTaskName())).thenReturn(null);

            // When & Then
            assertThatThrownBy(() -> submissionHistoryService.updateSubmissionWithApprovalDetails(mockSubmission))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Error getting approval details");

            verify(approvalClient, times(1)).detail(mockSubmission.getTaskName());
            assertThat(mockSubmission.getCurrentReviewer()).isEqualTo("");
        }

        @Test
        @DisplayName("Should handle unsuccessful response from approval client")
        void testUpdateSubmissionWithApprovalDetails_UnsuccessfulResponse() {
            // Given
            ApiResponse failedResponse = new ApiResponse();
            failedResponse.setSuccess(false);
            failedResponse.setMessage("Approval service error");
            failedResponse.setData(null);

            when(approvalClient.detail(mockSubmission.getTaskName())).thenReturn(failedResponse);

            // When & Then
            assertThatThrownBy(() -> submissionHistoryService.updateSubmissionWithApprovalDetails(mockSubmission))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Error getting approval details");

            verify(approvalClient, times(1)).detail(mockSubmission.getTaskName());
            assertThat(mockSubmission.getCurrentReviewer()).isEqualTo("");
        }

        @Test
        @DisplayName("Should handle response with null data")
        void testUpdateSubmissionWithApprovalDetails_NullData() {
            // Given
            ApiResponse responseWithNullData = new ApiResponse();
            responseWithNullData.setSuccess(true);
            responseWithNullData.setMessage("Success");
            responseWithNullData.setData(null);

            when(approvalClient.detail(mockSubmission.getTaskName())).thenReturn(responseWithNullData);

            // When & Then
            assertThatThrownBy(() -> submissionHistoryService.updateSubmissionWithApprovalDetails(mockSubmission))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Error getting approval details");

            verify(approvalClient, times(1)).detail(mockSubmission.getTaskName());
            assertThat(mockSubmission.getCurrentReviewer()).isEqualTo("");
        }

        @Test
        @DisplayName("Should handle approval client exception")
        void testUpdateSubmissionWithApprovalDetails_ClientException() {
            // Given
            when(approvalClient.detail(mockSubmission.getTaskName()))
                    .thenThrow(new RuntimeException("Connection timeout"));

            // When & Then
            assertThatThrownBy(() -> submissionHistoryService.updateSubmissionWithApprovalDetails(mockSubmission))
                    .isInstanceOf(CustomBadRequestException.class)
                    .hasMessageContaining("Error updating submission with approval details");

            verify(approvalClient, times(1)).detail(mockSubmission.getTaskName());
            assertThat(mockSubmission.getCurrentReviewer()).isEqualTo("");
        }
    }

    /**
     * Helper method to create a mock Submission entity
     */
    private Submission createMockSubmission() {
        Submission submission = new Submission();
        submission.setId(1L);
        submission.setTaskName("TASK-001");
        submission.setReferenceNumber("REF-001");
        submission.setCreatedBy("user1");
        submission.setSubmitterName("John Doe");
        submission.setSubmitterJob("Developer");
        submission.setStatus("PENDING");
        submission.setNip("123456789");
        submission.setName("John Doe");
        submission.setGrade("A");
        submission.setPaymentType("Salary");
        submission.setAmount(new BigDecimal("5000000"));
        submission.setDescription("Monthly salary");
        submission.setMonthOfProcess("January");
        submission.setYearOfProcess("2024");
        submission.setDirectorate("IT");
        submission.setSlik("-");
        submission.setSanction("-");
        submission.setTerminationDate("-");
        submission.setEligible(true);
        submission.setCurrentReviewer("");
        return submission;
    }

    /**
     * Helper method to create a mock ApiResponse
     */
    private ApiResponse createMockApiResponse() {
        ApiResponse response = new ApiResponse();
        response.setSuccess(true);
        response.setMessage("Success");
        
        DetailResponse detailResponse = new DetailResponse();
        detailResponse.setTaskName("TASK-001");
        
        response.setData(detailResponse);
        return response;
    }
}
